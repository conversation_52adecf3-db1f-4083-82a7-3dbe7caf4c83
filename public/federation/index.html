<!DOCTYPE html>
<html lang="en-us">
  <head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
    

<meta property="og:url" content="http://localhost:1313/federation/">
  <meta property="og:site_name" content="whitesky cloud platform">
  <meta property="og:title" content="Federated Cloud Grid">
  <meta property="og:description" content="How MSPs collaborate through whitesky to build a distributed, sovereign cloud network.">
  <meta property="og:locale" content="en_us">
  <meta property="og:type" content="article">


<meta name="description" content="Hardcoded description; the author should update :)" />
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>whitesky cloud platform</title>
    
  
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
  integrity="sha512-Avb2QiuDEEvB4bZJYdft2mNjVShBftLdPG8FJ0V7irTLQ8Uo0qcPxh4Plq7G5tGm0rU+1SPhVotteLpBERwTkw=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
/>
<link rel="icon" type="image/png" href="/images/favicon.png" />
<link href="https://fonts.googleapis.com/css?family=Open&#43;Sans:400,600" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="/css/style.css">
<link rel="stylesheet" type="text/css" href="/css/icons.css">

  </head>
  <body>
    
    
    <div id="preloader">
      <div id="status"></div>
    </div>
    

    

  
<nav class="navbar is-fresh is-transparent no-shadow" role="navigation" aria-label="main navigation">
  <div class="container">
    <div class="navbar-brand">
      <a class="navbar-item" href="/">
        <img src="/images/logo-blue-blacktext-small.png" alt="" width="160" height="28">
      </a>

      <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbar-menu">
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
      </a>
    </div>

      <div id="navbar-menu" class="navbar-menu is-static">

        <div class="navbar-end">
          <a href="/how" class="navbar-item is-secondary">
            How
          </a>
          <a href="/features" class="navbar-item is-secondary">
            Features
          </a>
          <a href="/federation" class="navbar-item is-secondary">
            Federation
          </a>
          <a href="/pricing" class="navbar-item is-secondary">
            Pricing
          </a>
          <a href="/blog#blog" class="navbar-item is-secondary">
            Blog
          </a>
          <div class="navbar-item has-dropdown is-hoverable">
            <a class="navbar-link">
              Resources
            </a>

            <div class="navbar-dropdown">
              <a href="https://try.whitesky.cloud/docs/en/" class="navbar-item">
                Documentation
              </a>
              <a href="https://try.whitesky.cloud/api/1/" class="navbar-item">
                APIs
              </a>
              <a href="/contact" class="navbar-item">
                Support
              </a>
              <a href="/contact" class="navbar-item">
                Contact
              </a>
            </div>
          </div>
          <a href="/" class="navbar-item">
            <span class="button signup-button rounded secondary-btn raised">
              Try now
            </span>
          </a>
        </div>
      </div>
  </div>
</nav>


<nav id="navbar-clone" class="navbar is-fresh is-transparent" role="navigation" aria-label="main navigation">
  <div class="container">
    <div class="navbar-brand">
      <a class="navbar-item" href="/">
        <img src="/images/logo-blue-blacktext-small.png" alt="" width="160" height="160">
      </a>

      <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="cloned-navbar-menu">
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
      </a>
    </div>

    <div id="cloned-navbar-menu" class="navbar-menu is-fixed">

      <div class="navbar-end">
        <a href="/how" class="navbar-item is-secondary">
          How
        </a>
        <a href="/features" class="navbar-item is-secondary">
          Features
        </a>
        <a href="/federation" class="navbar-item is-secondary">
          Federation
        </a>
        <a href="/pricing" class="navbar-item is-secondary">
          Pricing
        </a>
        <a href="/blog#blog" class="navbar-item is-secondary">
          Blog
        </a>
        <div class="navbar-item has-dropdown is-hoverable">
          <a class="navbar-link">
            Resources
          </a>

          <div class="navbar-dropdown">
            <a href="https://try.whitesky.cloud/docs/en/" class="navbar-item">
              Documentation
            </a>
            <a href="https://try.whitesky.cloud/api/1/" class="navbar-item">
              APIs
            </a>
            <a href="/contact" class="navbar-item">
              Support
            </a>
            <a href="/contact" class="navbar-item">
              Contact
            </a>
          </div>
        </div>
        <a href="/" class="navbar-item">
          <span class="button signup-button rounded secondary-btn raised">
            Try now
          </span>
        </a>
      </div>
    </div>
</div>
</nav>

<section class="section is-medium">
  <div class="container">
    <div class="columns">
      <div class="column is-centered-tablet-portrait">
        <h1 class="title section-title">Federated Cloud Grid</h1>
        <h5 class="subtitle is-5 is-muted"></h5>
        <div class="divider"></div>
      </div>
    </div>

    <div class="content">
      <p>A growing network of whitesky-powered cloud locations, built by and for MSPs and their clients.</p>
<h3 id="built-for-collaboration">Built for Collaboration</h3>
<p>whitesky enables MSPs to operate sovereign cloud locations under their own brand — and opt into a wider grid of compatible locations. Every participant can:</p>
<ul>
<li>Offer their excess capacity (VCPUs, RAM, block/object storage) to peers</li>
<li>Buy remote capacity to serve clients in new regions</li>
<li>Maintain control over their own infrastructure and customers</li>
</ul>
<h3 id="resell-expand-stay-independent">Resell, Expand, Stay Independent</h3>
<p>Each partner location remains independently operated, but thanks to federated integration through whitesky:</p>
<ul>
<li>Authentication, billing, and automation are unified</li>
<li>API keys and user portals span all federated cloud locations</li>
<li>Capacity is metered and invoiced transparently</li>
</ul>
<p>In addition, <strong>whitesky private clouds</strong> can target these public whitesky cloud locations for:</p>
<ul>
<li>Backup and disaster recovery</li>
<li>Bursting into additional compute/storage capacity</li>
<li>Offloading specific workloads to other trusted regions</li>
</ul>
<p>This flexibility allows organizations to keep critical operations in-house while leveraging the reach and resilience of the federated network.</p>
<h3 id="real-world-collaboration">Real-World Collaboration</h3>
<p>Whether you&rsquo;re an MSP in Belgium, a telco in Latin America, or a SaaS company scaling in Africa — you can join and contribute to the whitesky.cloud fabric.</p>
<p><strong>Resell services from fellow providers. Offer local resources globally.</strong></p>
<h3 id="federation-map">Federation Map</h3>
<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<style>
  #whitesky-map { height: 500px; width: 100%; margin-bottom: 2rem; }
</style>

<div id="whitesky-map"></div>

<script>
  const map = L.map('whitesky-map');

  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '&copy; OpenStreetMap contributors'
  }).addTo(map);

  const locations = [
    { name: "Gent, Belgium", coords: [51.05, 3.73] },
    { name: "Machelen, Belgium", coords: [50.901, 4.450] },
    { name: "Kampala, Uganda", coords: [0.3476, 32.5825] },
    { name: "Mauritius", coords: [-20.3484, 57.5522] },
    { name: "Warsaw, Poland", coords: [52.2297, 21.0122] },
    { name: "Budapest, Hungary", coords: [47.4979, 19.0402] },
    { name: "Schiphol, Netherlands", coords: [52.3105, 4.7683] }
  ];

  const bounds = [];

  locations.forEach(loc => {
    L.marker(loc.coords).addTo(map).bindPopup(loc.name);
    bounds.push(loc.coords);
  });

  map.fitBounds(bounds, { padding: [10,18] });
</script>

<h3 id="current-partners">Current Partners</h3>
<table>
  <thead>
      <tr>
          <th>Partner</th>
          <th>Country</th>
          <th>Services Available</th>
          <th>GPUs</th>
          <th>Available now</th>
      </tr>
  </thead>
  <tbody>
      <tr>
          <td><a href="https://www.varity.cloud">Varity</a></td>
          <td>Netherlands</td>
          <td>Cloudspaces, Objectspaces, Containerspaces</td>
          <td>no</td>
          <td>yes</td>
      </tr>
      <tr>
          <td><a href="https://www.cloudcom.be">CloudCom</a></td>
          <td>Belgium</td>
          <td>Cloudspaces, Containerspaces</td>
          <td>yes</td>
          <td>onboarding</td>
      </tr>
      <tr>
          <td><a href="https://www.lealgroup.mu/">LCI</a></td>
          <td>Mauritius</td>
          <td>Cloudspaces, Objectspaces, Containerspaces</td>
          <td>no</td>
          <td>yes</td>
      </tr>
      <tr>
          <td><a href="https://www.afriqloud.com">AfriQloud</a></td>
          <td>Uganda</td>
          <td>Cloudspaces, Objectspaces, Containerspaces</td>
          <td>no</td>
          <td>yes</td>
      </tr>
      <tr>
          <td><a href="https://www.roketelecom.ug">Roke Telecom</a></td>
          <td>Uganda</td>
          <td>Cloudspaces, Objectspaces, Containerspaces</td>
          <td>yes</td>
          <td>onboarding</td>
      </tr>
      <tr>
          <td><a href="https://www.whitesky.pl">whitesky Poland</a> (*)</td>
          <td>Poland</td>
          <td>Cloudspaces, Objectspaces, Containerspaces</td>
          <td>yes</td>
          <td>yes</td>
      </tr>
      <tr>
          <td><a href="https://whitesky.cloud">whitesky</a> (*)</td>
          <td>Belgium</td>
          <td>Cloudspaces, Objectspaces, Containerspaces</td>
          <td>yes</td>
          <td>yes</td>
      </tr>
      <tr>
          <td>undisclosed</td>
          <td>Hungary</td>
          <td>Cloudspaces, Containerspaces</td>
          <td>no</td>
          <td>yes</td>
      </tr>
  </tbody>
</table>
<p><em>(*) Testing and demonstration</em></p>
<h3 id="-join-the-federation">📞 Join the Federation</h3>
<p>Want to connect your infrastructure to the grid?<br>
<a href="/contact">Contact us</a> to learn more.</p>

    </div>
  </div>
</section>



  

  



    
    <div id="backtotop"><a href="#"></a></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.1/jquery.min.js"></script>
<script src="https://unpkg.com/feather-icons"></script>
<script src="/js/fresh.js"></script>
<script src="/js/jquery.panelslider.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/modernizr/2.8.3/modernizr.min.js"></script>

  </body>
</html>
